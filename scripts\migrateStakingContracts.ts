// Emergency Migration Script for StakingEngineLinear and StakingPool
// This script extracts all storage data from compromised contracts and migrates to new ones

import { ethers, upgrades } from "hardhat";
import * as fs from "fs";
import * as path from "path";

interface StakeInfo {
    amount: bigint;
    rewardDebt: bigint;
    lockPeriod: bigint;
    startTime: bigint;
    referrer: string;
    isActive: boolean;
}

interface ReferrerInfo {
    totalReferred: bigint;
    totalRewards: bigint;
    lastClaimTime: bigint;
}

interface MigrationData {
    // StakingEngineLinear data
    stakes: { [address: string]: StakeInfo[] };
    referrers: { [address: string]: ReferrerInfo };
    totalStaked: bigint;
    totalStaked90Days: bigint;
    totalStaked180Days: bigint;
    totalStaked365Days: bigint;
    allStakerAddresses: string[];
    stakerAddressesByPeriod: { [period: string]: string[] };
    
    // StakingPool data
    stakingPoolTokenBalance: bigint;
    rewardPoolTokenBalance: bigint;
    
    // Contract addresses
    oldStakingEngine: string;
    oldStakePool: string;
    oldRewardPool: string;
    tokenAddress: string;
}

async function main() {
    console.log("🚨 EMERGENCY MIGRATION SCRIPT 🚨");
    console.log("Extracting data from compromised contracts...\n");

    // Compromised contract addresses
    const COMPROMISED_STAKING_ENGINE = "******************************************";
    const COMPROMISED_STAKE_POOL = "******************************************";
    const COMPROMISED_REWARD_POOL = "******************************************"; // From checkProxyStorage.ts

    // Get contract instances
    const stakingEngine = await ethers.getContractAt("StakingEngineLinear", COMPROMISED_STAKING_ENGINE);
    const stakePool = await ethers.getContractAt("StakingPool", COMPROMISED_STAKE_POOL);
    const rewardPool = await ethers.getContractAt("StakingPool", COMPROMISED_REWARD_POOL);

    // Get token address
    const tokenAddress = await stakingEngine.token();
    const token = await ethers.getContractAt("IERC20", tokenAddress);
    
    console.log("📋 Contract Information:");
    console.log(`Token Address: ${tokenAddress}`);
    console.log(`Compromised StakingEngine: ${COMPROMISED_STAKING_ENGINE}`);
    console.log(`Compromised StakePool: ${COMPROMISED_STAKE_POOL}`);
    console.log(`Compromised RewardPool: ${COMPROMISED_REWARD_POOL}\n`);

    // Extract migration data
    const migrationData: MigrationData = {
        stakes: {},
        referrers: {},
        totalStaked: 0n,
        totalStaked90Days: 0n,
        totalStaked180Days: 0n,
        totalStaked365Days: 0n,
        allStakerAddresses: [],
        stakerAddressesByPeriod: {},
        stakingPoolTokenBalance: 0n,
        rewardPoolTokenBalance: 0n,
        oldStakingEngine: COMPROMISED_STAKING_ENGINE,
        oldStakePool: COMPROMISED_STAKE_POOL,
        oldRewardPool: COMPROMISED_REWARD_POOL,
        tokenAddress: tokenAddress
    };

    console.log("🔍 Step 1: Extracting global state variables...");
    
    try {
        migrationData.totalStaked = await stakingEngine.totalStaked();
        migrationData.totalStaked90Days = await stakingEngine.totalStaked90Days();
        migrationData.totalStaked180Days = await stakingEngine.totalStaked180Days();
        migrationData.totalStaked365Days = await stakingEngine.totalStaked365Days();
        
        console.log(`✅ Total Staked: ${ethers.formatEther(migrationData.totalStaked)}`);
        console.log(`✅ Total Staked 90 Days: ${ethers.formatEther(migrationData.totalStaked90Days)}`);
        console.log(`✅ Total Staked 180 Days: ${ethers.formatEther(migrationData.totalStaked180Days)}`);
        console.log(`✅ Total Staked 365 Days: ${ethers.formatEther(migrationData.totalStaked365Days)}`);
    } catch (error) {
        console.error("❌ Error extracting global state:", error);
    }

    console.log("\n🔍 Step 2: Extracting token balances...");
    
    try {
        migrationData.stakingPoolTokenBalance = await token.balanceOf(COMPROMISED_STAKE_POOL);
        migrationData.rewardPoolTokenBalance = await token.balanceOf(COMPROMISED_REWARD_POOL);
        
        console.log(`✅ StakePool Balance: ${ethers.formatEther(migrationData.stakingPoolTokenBalance)}`);
        console.log(`✅ RewardPool Balance: ${ethers.formatEther(migrationData.rewardPoolTokenBalance)}`);
    } catch (error) {
        console.error("❌ Error extracting token balances:", error);
    }

    console.log("\n🔍 Step 3: Extracting staker addresses...");
    
    try {
        // Get all staker addresses - we'll need to iterate through events or use a different approach
        // Since we can't directly access dynamic arrays, we'll extract from events
        const filter = stakingEngine.filters.TokensStaked();
        const events = await stakingEngine.queryFilter(filter, 0, "latest");
        
        const uniqueStakers = new Set<string>();
        for (const event of events) {
            if (event.args) {
                uniqueStakers.add(event.args.user);
            }
        }
        
        migrationData.allStakerAddresses = Array.from(uniqueStakers);
        console.log(`✅ Found ${migrationData.allStakerAddresses.length} unique stakers`);
    } catch (error) {
        console.error("❌ Error extracting staker addresses:", error);
        // Fallback: we'll need manual input of staker addresses
        console.log("⚠️  You may need to provide staker addresses manually");
    }

    console.log("\n🔍 Step 4: Extracting individual stake data...");
    
    for (const stakerAddress of migrationData.allStakerAddresses) {
        try {
            console.log(`   Processing staker: ${stakerAddress}`);
            
            // Get number of stakes for this user
            let stakeCount = 0;
            const userStakes: StakeInfo[] = [];
            
            // Try to get stakes until we hit an error (indicating end of array)
            while (true) {
                try {
                    const stake = await stakingEngine.stakes(stakerAddress, stakeCount);
                    userStakes.push({
                        amount: stake.amount,
                        rewardDebt: stake.rewardDebt,
                        lockPeriod: stake.lockPeriod,
                        startTime: stake.startTime,
                        referrer: stake.referrer,
                        isActive: stake.isActive
                    });
                    stakeCount++;
                } catch {
                    break; // End of stakes for this user
                }
            }
            
            if (userStakes.length > 0) {
                migrationData.stakes[stakerAddress] = userStakes;
                console.log(`     ✅ Found ${userStakes.length} stakes`);
            }
            
            // Get referrer info if exists
            try {
                const referrerInfo = await stakingEngine.referrers(stakerAddress);
                if (referrerInfo.totalReferred > 0n || referrerInfo.totalRewards > 0n) {
                    migrationData.referrers[stakerAddress] = {
                        totalReferred: referrerInfo.totalReferred,
                        totalRewards: referrerInfo.totalRewards,
                        lastClaimTime: referrerInfo.lastClaimTime
                    };
                    console.log(`     ✅ Found referrer data`);
                }
            } catch (error) {
                // No referrer data for this address
            }
            
        } catch (error) {
            console.error(`❌ Error processing staker ${stakerAddress}:`, error);
        }
    }

    // Save migration data to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `migration-data-${timestamp}.json`;
    const filepath = path.join(__dirname, filename);
    
    console.log(`\n💾 Saving migration data to: ${filename}`);
    fs.writeFileSync(filepath, JSON.stringify(migrationData, (key, value) =>
        typeof value === 'bigint' ? value.toString() : value
    , 2));
    
    console.log("\n📊 Migration Data Summary:");
    console.log(`   Total Stakers: ${migrationData.allStakerAddresses.length}`);
    console.log(`   Total Stakes: ${Object.values(migrationData.stakes).reduce((sum, stakes) => sum + stakes.length, 0)}`);
    console.log(`   Total Referrers: ${Object.keys(migrationData.referrers).length}`);
    console.log(`   Total Staked Amount: ${ethers.formatEther(migrationData.totalStaked)}`);
    console.log(`   StakePool Balance: ${ethers.formatEther(migrationData.stakingPoolTokenBalance)}`);
    console.log(`   RewardPool Balance: ${ethers.formatEther(migrationData.rewardPoolTokenBalance)}`);
    
    console.log("\n✅ Data extraction complete!");
    console.log(`📁 Migration data saved to: ${filename}`);
    console.log("\n🚀 Next steps:");
    console.log("1. Deploy new StakingEngineLinear and StakingPool contracts");
    console.log("2. Run the migration deployment script with this data");
    console.log("3. Transfer funds from old contracts to new ones");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Migration failed:", error);
        process.exit(1);
    });

// Usage: npx hardhat run scripts/migrateStakingContracts.ts --network base
