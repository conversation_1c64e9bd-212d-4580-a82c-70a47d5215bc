// Migrate stake data to new StakingEngineLinear contract
// This script restores all user stakes and referrer data to the new contract

import { ethers } from "hardhat";
import * as fs from "fs";
import * as path from "path";

interface StakeInfo {
    amount: string;
    rewardDebt: string;
    lockPeriod: string;
    startTime: string;
    referrer: string;
    isActive: boolean;
}

interface ReferrerInfo {
    totalReferred: string;
    totalRewards: string;
    lastClaimTime: string;
}

interface MigrationData {
    stakes: { [address: string]: StakeInfo[] };
    referrers: { [address: string]: ReferrerInfo };
    totalStaked: string;
    totalStaked90Days: string;
    totalStaked180Days: string;
    totalStaked365Days: string;
    allStakerAddresses: string[];
    stakingPoolTokenBalance: string;
    rewardPoolTokenBalance: string;
    tokenAddress: string;
}

interface DeploymentInfo {
    contracts: {
        stakingEngineLinear: string;
        stakePool: string;
        rewardPool: string;
    };
}

async function main() {
    console.log("🔄 STAKE DATA MIGRATION SCRIPT 🔄");
    console.log("Migrating user stakes to new contract...\n");

    const [deployer] = await ethers.getSigners();
    console.log("Migrating with account:", deployer.address);

    // Load migration data
    const migrationDataFile = process.env.MIGRATION_DATA_FILE;
    const deploymentInfoFile = process.env.DEPLOYMENT_INFO_FILE;
    
    if (!migrationDataFile || !deploymentInfoFile) {
        throw new Error("MIGRATION_DATA_FILE and DEPLOYMENT_INFO_FILE environment variables required");
    }

    console.log(`📁 Loading migration data from: ${migrationDataFile}`);
    const migrationData: MigrationData = JSON.parse(
        fs.readFileSync(path.join(__dirname, migrationDataFile), 'utf8')
    );

    console.log(`📁 Loading deployment info from: ${deploymentInfoFile}`);
    const deploymentInfo: DeploymentInfo = JSON.parse(
        fs.readFileSync(path.join(__dirname, deploymentInfoFile), 'utf8')
    );

    // Get new contract instances
    const newStakingEngine = await ethers.getContractAt(
        "StakingEngineLinear", 
        deploymentInfo.contracts.stakingEngineLinear
    );
    
    const token = await ethers.getContractAt("IERC20", migrationData.tokenAddress);

    console.log("\n📋 Migration Summary:");
    console.log(`New StakingEngine: ${deploymentInfo.contracts.stakingEngineLinear}`);
    console.log(`Total Stakers to migrate: ${migrationData.allStakerAddresses.length}`);
    console.log(`Total Stakes: ${Object.values(migrationData.stakes).reduce((sum, stakes) => sum + stakes.length, 0)}`);

    // Check if we need a special migration function
    console.log("\n⚠️  IMPORTANT: This script assumes you have added migration functions to your StakingEngineLinear contract.");
    console.log("Required functions:");
    console.log("- migrateStake(address user, uint256 amount, uint256 rewardDebt, uint256 lockPeriod, uint256 startTime, address referrer, bool isActive)");
    console.log("- migrateReferrer(address referrer, uint256 totalReferred, uint256 totalRewards, uint256 lastClaimTime)");
    console.log("- setTotalStaked(uint256 total, uint256 total90, uint256 total180, uint256 total365)");

    // Batch size for processing
    const BATCH_SIZE = 10;
    let processedStakers = 0;
    let processedStakes = 0;

    console.log("\n🔄 Step 1: Migrating global state...");
    
    try {
        // Note: You'll need to add these functions to your StakingEngineLinear contract
        console.log("Setting total staked amounts...");
        console.log(`Total Staked: ${ethers.formatEther(migrationData.totalStaked)}`);
        console.log(`Total Staked 90 Days: ${ethers.formatEther(migrationData.totalStaked90Days)}`);
        console.log(`Total Staked 180 Days: ${ethers.formatEther(migrationData.totalStaked180Days)}`);
        console.log(`Total Staked 365 Days: ${ethers.formatEther(migrationData.totalStaked365Days)}`);
        
        // Uncomment when you add the migration function:
        // await newStakingEngine.setTotalStaked(
        //     migrationData.totalStaked,
        //     migrationData.totalStaked90Days,
        //     migrationData.totalStaked180Days,
        //     migrationData.totalStaked365Days
        // );
        
        console.log("⚠️  Manual step required: Set total staked amounts in new contract");
    } catch (error) {
        console.error("❌ Error setting global state:", error);
    }

    console.log("\n🔄 Step 2: Migrating individual stakes...");

    // Process stakers in batches
    const stakerAddresses = Object.keys(migrationData.stakes);
    
    for (let i = 0; i < stakerAddresses.length; i += BATCH_SIZE) {
        const batch = stakerAddresses.slice(i, i + BATCH_SIZE);
        
        console.log(`\n📦 Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(stakerAddresses.length / BATCH_SIZE)}`);
        
        for (const stakerAddress of batch) {
            const stakes = migrationData.stakes[stakerAddress];
            
            console.log(`   👤 Migrating ${stakes.length} stakes for ${stakerAddress}`);
            
            for (const stake of stakes) {
                try {
                    console.log(`      💰 Stake: ${ethers.formatEther(stake.amount)} tokens, ${Number(stake.lockPeriod) / (24 * 60 * 60)} days`);
                    
                    // Note: You'll need to add this migration function to your contract
                    // await newStakingEngine.migrateStake(
                    //     stakerAddress,
                    //     stake.amount,
                    //     stake.rewardDebt,
                    //     stake.lockPeriod,
                    //     stake.startTime,
                    //     stake.referrer,
                    //     stake.isActive
                    // );
                    
                    processedStakes++;
                    console.log(`         ✅ Migrated stake ${processedStakes}`);
                    
                } catch (error) {
                    console.error(`         ❌ Error migrating stake:`, error);
                }
            }
            
            processedStakers++;
        }
        
        // Small delay between batches to avoid overwhelming the network
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log("\n🔄 Step 3: Migrating referrer data...");

    const referrerAddresses = Object.keys(migrationData.referrers);
    let processedReferrers = 0;

    for (const referrerAddress of referrerAddresses) {
        const referrerInfo = migrationData.referrers[referrerAddress];
        
        try {
            console.log(`   👥 Migrating referrer: ${referrerAddress}`);
            console.log(`      Total Referred: ${referrerInfo.totalReferred}`);
            console.log(`      Total Rewards: ${ethers.formatEther(referrerInfo.totalRewards)}`);
            
            // Note: You'll need to add this migration function to your contract
            // await newStakingEngine.migrateReferrer(
            //     referrerAddress,
            //     referrerInfo.totalReferred,
            //     referrerInfo.totalRewards,
            //     referrerInfo.lastClaimTime
            // );
            
            processedReferrers++;
            console.log(`      ✅ Migrated referrer ${processedReferrers}/${referrerAddresses.length}`);
            
        } catch (error) {
            console.error(`      ❌ Error migrating referrer:`, error);
        }
    }

    console.log("\n📊 Migration Summary:");
    console.log(`✅ Processed Stakers: ${processedStakers}/${migrationData.allStakerAddresses.length}`);
    console.log(`✅ Processed Stakes: ${processedStakes}`);
    console.log(`✅ Processed Referrers: ${processedReferrers}/${referrerAddresses.length}`);

    // Verify migration
    console.log("\n🔍 Step 4: Verifying migration...");
    
    try {
        const newTotalStaked = await newStakingEngine.totalStaked();
        console.log(`New contract total staked: ${ethers.formatEther(newTotalStaked)}`);
        console.log(`Expected total staked: ${ethers.formatEther(migrationData.totalStaked)}`);
        
        if (newTotalStaked.toString() === migrationData.totalStaked) {
            console.log("✅ Total staked amounts match!");
        } else {
            console.log("⚠️  Total staked amounts don't match - manual verification needed");
        }
    } catch (error) {
        console.log("⚠️  Could not verify total staked - manual verification needed");
    }

    console.log("\n✅ Stake data migration complete!");
    console.log("\n🚨 IMPORTANT NOTES:");
    console.log("1. This script shows the migration process but requires custom migration functions");
    console.log("2. Add the following functions to your StakingEngineLinear contract:");
    console.log("   - migrateStake() - for individual stake migration");
    console.log("   - migrateReferrer() - for referrer data migration");
    console.log("   - setTotalStaked() - for global state migration");
    console.log("3. Ensure only admin can call these migration functions");
    console.log("4. Consider adding a migration mode that disables normal operations during migration");
    console.log("5. Verify all data after migration before enabling normal operations");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Migration failed:", error);
        process.exit(1);
    });

// Usage:
// MIGRATION_DATA_FILE=migration-data-2024-01-01T12-00-00-000Z.json \
// DEPLOYMENT_INFO_FILE=deployment-2024-01-01T12-30-00-000Z.json \
// npx hardhat run scripts/migrateStakeData.ts --network base
