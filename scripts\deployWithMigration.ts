// Deploy new StakingEngineLinearWithMigration and StakingPool contracts with migration data
// This script deploys fresh contracts with migration capabilities for emergency migration

import { ethers, upgrades } from "hardhat";
import * as fs from "fs";
import * as path from "path";

interface StakeInfo {
    amount: string;
    rewardDebt: string;
    lockPeriod: string;
    startTime: string;
    referrer: string;
    isActive: boolean;
}

interface ReferrerInfo {
    totalReferred: string;
    totalRewards: string;
    lastClaimTime: string;
}

interface MigrationData {
    stakes: { [address: string]: StakeInfo[] };
    referrers: { [address: string]: ReferrerInfo };
    totalStaked: string;
    totalStaked90Days: string;
    totalStaked180Days: string;
    totalStaked365Days: string;
    allStakerAddresses: string[];
    stakerAddressesByPeriod: { [period: string]: string[] };
    stakingPoolTokenBalance: string;
    rewardPoolTokenBalance: string;
    oldStakingEngine: string;
    oldStakePool: string;
    oldRewardPool: string;
    tokenAddress: string;
}

async function main() {
    console.log("🚀 EMERGENCY DEPLOYMENT WITH MIGRATION 🚀");
    console.log("Deploying new contracts and migrating data...\n");

    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);

    // Load migration data
    const migrationDataFile = process.env.MIGRATION_DATA_FILE;
    if (!migrationDataFile) {
        throw new Error("MIGRATION_DATA_FILE environment variable not set");
    }

    const migrationDataPath = path.join(__dirname, migrationDataFile);
    if (!fs.existsSync(migrationDataPath)) {
        throw new Error(`Migration data file not found: ${migrationDataPath}`);
    }

    console.log(`📁 Loading migration data from: ${migrationDataFile}`);
    const migrationData: MigrationData = JSON.parse(fs.readFileSync(migrationDataPath, 'utf8'));

    // Environment variables
    const initialOwner = process.env.INITIAL_OWNER || deployer.address;
    const initialAdmin = process.env.INITIAL_ADMIN || deployer.address;

    console.log("\n📋 Configuration:");
    console.log(`Initial Owner: ${initialOwner}`);
    console.log(`Initial Admin: ${initialAdmin}`);
    console.log(`Token Address: ${migrationData.tokenAddress}`);
    console.log(`Migrating ${migrationData.allStakerAddresses.length} stakers`);

    // Get token contract
    const token = await ethers.getContractAt("IERC20", migrationData.tokenAddress);

    console.log("\n🏗️  Step 1: Deploying new StakingPool contracts...");

    // Deploy StakingPool Implementation
    const StakingPool = await ethers.getContractFactory("StakingPool");
    const stakingPoolImplementation = await StakingPool.deploy();
    await stakingPoolImplementation.waitForDeployment();
    const stakingPoolImplAddress = await stakingPoolImplementation.getAddress();
    console.log("✅ StakingPool implementation deployed to:", stakingPoolImplAddress);

    // Deploy StakePool Proxy
    const StakingPoolProxy = await ethers.getContractFactory("ERC1967Proxy");
    const stakePoolInitData = stakingPoolImplementation.interface.encodeFunctionData(
        "initialize",
        [migrationData.tokenAddress, initialOwner, initialAdmin]
    );
    
    const stakePoolProxy = await StakingPoolProxy.deploy(
        stakingPoolImplAddress,
        stakePoolInitData
    );
    await stakePoolProxy.waitForDeployment();
    const stakePoolAddress = await stakePoolProxy.getAddress();
    console.log("✅ New StakePool proxy deployed to:", stakePoolAddress);

    // Deploy RewardPool Proxy
    const rewardPoolInitData = stakingPoolImplementation.interface.encodeFunctionData(
        "initialize",
        [migrationData.tokenAddress, initialOwner, initialAdmin]
    );
    
    const rewardPoolProxy = await StakingPoolProxy.deploy(
        stakingPoolImplAddress,
        rewardPoolInitData
    );
    await rewardPoolProxy.waitForDeployment();
    const rewardPoolAddress = await rewardPoolProxy.getAddress();
    console.log("✅ New RewardPool proxy deployed to:", rewardPoolAddress);

    console.log("\n🏗️  Step 2: Deploying new StakingEngineLinearWithMigration...");

    // Deploy StakingEngineLinearWithMigration using UUPS proxy pattern
    const StakingEngineLinearWithMigration = await ethers.getContractFactory("StakingEngineLinearWithMigration");
    const stakingEngine = await upgrades.deployProxy(
        StakingEngineLinearWithMigration,
        [
            migrationData.tokenAddress,
            stakePoolAddress,
            rewardPoolAddress,
            initialOwner,
            initialAdmin
        ],
        { kind: 'uups', initializer: 'initialize' }
    );

    await stakingEngine.waitForDeployment();
    const stakingEngineAddress = await stakingEngine.getAddress();
    console.log("✅ New StakingEngineLinearWithMigration deployed to:", stakingEngineAddress);

    // Set up permissions
    console.log("\n🔐 Step 3: Setting up permissions...");
    const stakePool = await ethers.getContractAt("StakingPool", stakePoolAddress);
    const rewardPool = await ethers.getContractAt("StakingPool", rewardPoolAddress);

    await stakePool.connect(deployer).setStakingEngine(stakingEngineAddress);
    console.log("✅ StakePool configured with new StakingEngine");

    await rewardPool.connect(deployer).setStakingEngine(stakingEngineAddress);
    console.log("✅ RewardPool configured with new StakingEngine");

    console.log("\n💰 Step 4: Transferring funds from old contracts...");
    
    // Get old contracts
    const oldStakePool = await ethers.getContractAt("StakingPool", migrationData.oldStakePool);
    const oldRewardPool = await ethers.getContractAt("StakingPool", migrationData.oldRewardPool);

    // Check if we have admin access to transfer funds
    try {
        const stakePoolBalance = ethers.parseEther(migrationData.stakingPoolTokenBalance);
        const rewardPoolBalance = ethers.parseEther(migrationData.rewardPoolTokenBalance);

        console.log(`Attempting to transfer ${ethers.formatEther(stakePoolBalance)} from old StakePool...`);
        console.log(`Attempting to transfer ${ethers.formatEther(rewardPoolBalance)} from old RewardPool...`);

        // Note: These transfers will only work if the deployer has admin access
        // If not, you'll need to do this manually through governance or admin functions
        
        console.log("⚠️  Manual fund transfer required:");
        console.log(`   Transfer ${ethers.formatEther(stakePoolBalance)} tokens from ${migrationData.oldStakePool} to ${stakePoolAddress}`);
        console.log(`   Transfer ${ethers.formatEther(rewardPoolBalance)} tokens from ${migrationData.oldRewardPool} to ${rewardPoolAddress}`);
        
    } catch (error) {
        console.log("⚠️  Could not automatically transfer funds. Manual transfer required.");
    }

    console.log("\n📊 Step 5: Summary of new deployment...");
    console.log("New Contract Addresses:");
    console.log(`   StakingEngineLinearWithMigration: ${stakingEngineAddress}`);
    console.log(`   StakePool: ${stakePoolAddress}`);
    console.log(`   RewardPool: ${rewardPoolAddress}`);
    console.log(`   StakingPool Implementation: ${stakingPoolImplAddress}`);

    // Save deployment info
    const deploymentInfo = {
        timestamp: new Date().toISOString(),
        network: (await ethers.provider.getNetwork()).name,
        deployer: deployer.address,
        contracts: {
            stakingEngineLinearWithMigration: stakingEngineAddress,
            stakePool: stakePoolAddress,
            rewardPool: rewardPoolAddress,
            stakingPoolImplementation: stakingPoolImplAddress
        },
        migrationData: {
            totalStakers: migrationData.allStakerAddresses.length,
            totalStaked: migrationData.totalStaked,
            stakingPoolBalance: migrationData.stakingPoolTokenBalance,
            rewardPoolBalance: migrationData.rewardPoolTokenBalance
        },
        oldContracts: {
            stakingEngine: migrationData.oldStakingEngine,
            stakePool: migrationData.oldStakePool,
            rewardPool: migrationData.oldRewardPool
        }
    };

    const deploymentFile = `deployment-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    fs.writeFileSync(path.join(__dirname, deploymentFile), JSON.stringify(deploymentInfo, null, 2));
    
    console.log(`\n📁 Deployment info saved to: ${deploymentFile}`);
    
    console.log("\n✅ Emergency deployment complete!");
    console.log("\n🚨 CRITICAL NEXT STEPS:");
    console.log("1. ⚠️  IMMEDIATELY transfer all funds from old contracts to new ones");
    console.log("2. 🔄 Run the data migration script to restore user stakes");
    console.log("3. 🔒 Pause or disable the old contracts if possible");
    console.log("4. 📢 Notify users about the new contract addresses");
    console.log("5. ✅ Verify all contracts on block explorer");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Deployment failed:", error);
        process.exit(1);
    });

// Usage: 
// MIGRATION_DATA_FILE=migration-data-2024-01-01T12-00-00-000Z.json \
// INITIAL_OWNER=0x... \
// INITIAL_ADMIN=0x... \
// npx hardhat run scripts/deployWithMigration.ts --network base
