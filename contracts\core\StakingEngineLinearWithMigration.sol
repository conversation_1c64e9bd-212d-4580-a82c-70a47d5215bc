// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "./StakingEngineLinear.sol";

/**
 * @title StakingEngineLinearWithMigration
 * @notice Extended version of StakingEngineLinear with emergency migration functions
 * @dev This contract adds migration capabilities for emergency situations
 */
contract StakingEngineLinearWithMigration is StakingEngineLinear {
    
    // Migration state
    bool public migrationMode;
    mapping(address => bool) public migratedUsers;
    uint256 public totalMigratedStakes;
    
    // Events
    event MigrationModeEnabled();
    event MigrationModeDisabled();
    event StakeMigrated(address indexed user, uint256 stakeIndex, uint256 amount, uint256 lockPeriod);
    event ReferrerMigrated(address indexed referrer, uint256 totalReferred, uint256 totalRewards);
    event GlobalStateMigrated(uint256 totalStaked, uint256 total90, uint256 total180, uint256 total365);
    
    // Errors
    error MigrationModeActive();
    error MigrationModeInactive();
    error UserAlreadyMigrated();
    error InvalidMigrationData();
    
    /**
     * @notice Enable migration mode - disables normal staking operations
     * @dev Only admin can call this function
     */
    function enableMigrationMode() external onlyRole(ProposalTypes.ADMIN_ROLE) {
        migrationMode = true;
        _pause(); // Pause normal operations
        emit MigrationModeEnabled();
    }
    
    /**
     * @notice Disable migration mode - re-enables normal staking operations
     * @dev Only admin can call this function
     */
    function disableMigrationMode() external onlyRole(ProposalTypes.ADMIN_ROLE) {
        migrationMode = false;
        _unpause(); // Resume normal operations
        emit MigrationModeDisabled();
    }
    
    /**
     * @notice Migrate a single stake for a user
     * @dev Only admin can call this during migration mode
     */
    function migrateStake(
        address user,
        uint256 amount,
        uint256 rewardDebt,
        uint256 lockPeriod,
        uint256 startTime,
        address referrer,
        bool isActive
    ) external onlyRole(ProposalTypes.ADMIN_ROLE) {
        if (!migrationMode) revert MigrationModeInactive();
        if (amount == 0) revert InvalidMigrationData();
        
        // Validate lock period
        if (lockPeriod != LOCK_PERIOD_1 && 
            lockPeriod != LOCK_PERIOD_2 && 
            lockPeriod != LOCK_PERIOD_3) {
            revert InvalidMigrationData();
        }
        
        // Add the stake directly to storage
        stakes[user].push(
            StakeInfo({
                amount: amount,
                rewardDebt: rewardDebt,
                lockPeriod: lockPeriod,
                startTime: startTime,
                referrer: referrer,
                isActive: isActive
            })
        );
        
        // Update tracking if this is the first stake for this user
        if (!isKnownStaker[user]) {
            isKnownStaker[user] = true;
            allStakerAddresses.push(user);
        }
        
        if (!isStakerInPeriod[lockPeriod][user]) {
            isStakerInPeriod[lockPeriod][user] = true;
            stakerAddressesByPeriod[lockPeriod].push(user);
        }
        
        totalMigratedStakes++;
        
        emit StakeMigrated(user, stakes[user].length - 1, amount, lockPeriod);
    }
    
    /**
     * @notice Migrate multiple stakes for a user in one transaction
     * @dev Only admin can call this during migration mode
     */
    function migrateMultipleStakes(
        address user,
        uint256[] calldata amounts,
        uint256[] calldata rewardDebts,
        uint256[] calldata lockPeriods,
        uint256[] calldata startTimes,
        address[] calldata referrers,
        bool[] calldata isActiveFlags
    ) external onlyRole(ProposalTypes.ADMIN_ROLE) {
        if (!migrationMode) revert MigrationModeInactive();
        
        uint256 length = amounts.length;
        if (length != rewardDebts.length || 
            length != lockPeriods.length || 
            length != startTimes.length || 
            length != referrers.length || 
            length != isActiveFlags.length) {
            revert InvalidMigrationData();
        }
        
        for (uint256 i = 0; i < length; i++) {
            if (amounts[i] == 0) revert InvalidMigrationData();
            
            // Validate lock period
            if (lockPeriods[i] != LOCK_PERIOD_1 && 
                lockPeriods[i] != LOCK_PERIOD_2 && 
                lockPeriods[i] != LOCK_PERIOD_3) {
                revert InvalidMigrationData();
            }
            
            stakes[user].push(
                StakeInfo({
                    amount: amounts[i],
                    rewardDebt: rewardDebts[i],
                    lockPeriod: lockPeriods[i],
                    startTime: startTimes[i],
                    referrer: referrers[i],
                    isActive: isActiveFlags[i]
                })
            );
            
            emit StakeMigrated(user, stakes[user].length - 1, amounts[i], lockPeriods[i]);
        }
        
        // Update tracking if this is the first stakes for this user
        if (!isKnownStaker[user]) {
            isKnownStaker[user] = true;
            allStakerAddresses.push(user);
        }
        
        // Update period tracking
        for (uint256 i = 0; i < length; i++) {
            if (!isStakerInPeriod[lockPeriods[i]][user]) {
                isStakerInPeriod[lockPeriods[i]][user] = true;
                stakerAddressesByPeriod[lockPeriods[i]].push(user);
            }
        }
        
        totalMigratedStakes += length;
    }
    
    /**
     * @notice Migrate referrer data
     * @dev Only admin can call this during migration mode
     */
    function migrateReferrer(
        address referrer,
        uint256 totalReferred,
        uint256 totalRewards,
        uint256 lastClaimTime
    ) external onlyRole(ProposalTypes.ADMIN_ROLE) {
        if (!migrationMode) revert MigrationModeInactive();
        
        referrers[referrer] = ReferrerInfo({
            totalReferred: totalReferred,
            totalRewards: totalRewards,
            lastClaimTime: lastClaimTime
        });
        
        emit ReferrerMigrated(referrer, totalReferred, totalRewards);
    }
    
    /**
     * @notice Set global staked amounts
     * @dev Only admin can call this during migration mode
     */
    function setTotalStaked(
        uint256 _totalStaked,
        uint256 _totalStaked90Days,
        uint256 _totalStaked180Days,
        uint256 _totalStaked365Days
    ) external onlyRole(ProposalTypes.ADMIN_ROLE) {
        if (!migrationMode) revert MigrationModeInactive();
        
        totalStaked = _totalStaked;
        totalStaked90Days = _totalStaked90Days;
        totalStaked180Days = _totalStaked180Days;
        totalStaked365Days = _totalStaked365Days;
        
        emit GlobalStateMigrated(_totalStaked, _totalStaked90Days, _totalStaked180Days, _totalStaked365Days);
    }
    
    /**
     * @notice Mark a user as migrated
     * @dev Only admin can call this during migration mode
     */
    function markUserMigrated(address user) external onlyRole(ProposalTypes.ADMIN_ROLE) {
        if (!migrationMode) revert MigrationModeInactive();
        migratedUsers[user] = true;
    }
    
    /**
     * @notice Get migration status
     */
    function getMigrationStatus() external view returns (
        bool inMigrationMode,
        uint256 totalMigrated,
        uint256 totalStakers
    ) {
        return (migrationMode, totalMigratedStakes, allStakerAddresses.length);
    }
    
    /**
     * @notice Override stakeToken function to prevent staking during migration
     */
    function stakeToken(uint256 amount, uint256 lockPeriod) public override whenNotPaused nonReentrant {
        if (migrationMode) revert MigrationModeActive();
        super.stakeToken(amount, lockPeriod);
    }

    /**
     * @notice Override stakeTokenWithReferrer function to prevent staking during migration
     */
    function stakeTokenWithReferrer(
        uint256 amount,
        uint256 lockPeriod,
        address referrer
    ) public override whenNotPaused nonReentrant {
        if (migrationMode) revert MigrationModeActive();
        super.stakeTokenWithReferrer(amount, lockPeriod, referrer);
    }
    
    /**
     * @notice Override unstakeToken function to prevent unstaking during migration
     */
    function unstakeToken(uint256 stakeIndex) public override whenNotPaused nonReentrant {
        if (migrationMode) revert MigrationModeActive();
        super.unstakeToken(stakeIndex);
    }
    
    /**
     * @notice Override claimStakerReward function to prevent claiming during migration
     */
    function claimStakerReward(uint256 stakeIndex) public override whenNotPaused nonReentrant {
        if (migrationMode) revert MigrationModeActive();
        super.claimStakerReward(stakeIndex);
    }

    /**
     * @notice Override claimReferrerReward function to prevent claiming during migration
     */
    function claimReferrerReward(uint256 rewardIndex) public override whenNotPaused nonReentrant {
        if (migrationMode) revert MigrationModeActive();
        super.claimReferrerReward(rewardIndex);
    }
}
